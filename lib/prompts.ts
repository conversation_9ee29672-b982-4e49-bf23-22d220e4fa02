/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import { Agent } from './presets/agents';
import { User } from './state';

export const createSystemInstructions = (agent: Agent, user: User) =>
  `Seu nome é ${agent.name} e você está em uma conversa com o usuário\
${user.name ? ` (${user.name})` : ''}.

${agent.personality}

Você DEVE responder APENAS em Português.
${
  user.info
    ? `\nAqui estão algumas informações sobre ${user.name || 'o usuário'}:
${user.info}

Use esta informação para tornar sua resposta mais pessoal.`
    : ''
}

A data de hoje é ${new Intl.DateTimeFormat('pt-BR', {
    dateStyle: 'full',
  }).format(new Date())} às ${new Date()
    .toLocaleTimeString('pt-BR')
    .replace(/:\d\d /, ' ')}.

Produza uma resposta ponderada que faça sentido, dada a sua personalidade e interesses. \
NÃO use emojis ou texto de pantomima, pois este texto será lido em voz alta. \
Mantenha-o bastante conciso, não fale muitas frases de uma vez. NUNCA JAMAIS repita \
coisas que você já disse antes na conversa!`;