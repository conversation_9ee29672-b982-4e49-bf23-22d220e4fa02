/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { audioContext } from "./utils";
import AudioRecordingWorklet from "./worklets/audio-processing";
import VolMeterWorket from "./worklets/vol-meter";

import { createWorketFromSrc } from "./audioworklet-registry";
import EventEmitter from "eventemitter3";

function arrayBufferToBase64(buffer: ArrayBuffer) {
  var binary = "";
  var bytes = new Uint8Array(buffer);
  var len = bytes.byteLength;
  for (var i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

// Define event types for AudioRecorder
interface AudioRecorderEventTypes {
  data: (base64AudioData: string) => void;
  volume: (volumeLevel: number) => void;
  stop: () => void;
}

export class AudioRecorder extends EventEmitter<AudioRecorderEventTypes> {
  stream: MediaStream | undefined;
  audioContext: AudioContext | undefined;
  source: MediaStreamAudioSourceNode | undefined;
  recording: boolean = false;
  recordingWorklet: AudioWorkletNode | undefined;
  vuWorklet: AudioWorkletNode | undefined;

  private starting: Promise<void> | null = null;
  private lastPermissionCheck: number = 0;
  private permissionCheckInterval: number = 5000; // 5 segundos

  constructor(public sampleRate = 16000) {
    super();
  }

  async start(existingStream?: MediaStream): Promise<void> {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.error("AudioRecorder: getUserMedia not supported.");
      throw new Error("getUserMedia not supported.");
    }

    if (this.starting) {
      return this.starting;
    }
    if (this.recording) {
      return Promise.resolve();
    }

    this.starting = new Promise(async (resolve, reject) => {
      try {
        // Detecta iOS e Safari
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isSafari = /^((?!chrome|android).)*safari/i.test(
          navigator.userAgent
        );

        // Configurações de áudio otimizadas para iOS/Safari
        const audioConstraints: MediaTrackConstraints = {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        };

        // Só define sampleRate se não for iOS/Safari (eles não suportam bem)
        if (!isIOS && !isSafari) {
          audioConstraints.sampleRate = this.sampleRate;
        }

        // Para Safari, evita verificações desnecessárias de permissão
        // se uma verificação recente foi feita
        const now = Date.now();
        const recentlyChecked =
          now - this.lastPermissionCheck < this.permissionCheckInterval;

        if (!isSafari || !recentlyChecked) {
          // Verifica se já temos permissão antes de solicitar
          try {
            const permissions = await navigator.permissions.query({
              name: "microphone" as PermissionName,
            });
            if (permissions.state === "denied") {
              throw new Error(
                "Permissão do microfone foi negada. Por favor, permita o acesso ao microfone nas configurações do navegador."
              );
            }
            this.lastPermissionCheck = now;
          } catch (permError) {
            // Alguns navegadores não suportam permissions.query para microfone
            console.warn("Não foi possível verificar permissões:", permError);
          }
        }

        // Usa stream existente se fornecido (para evitar dupla solicitação no Safari)
        if (existingStream && existingStream.active) {
          this.stream = existingStream;
          console.log("Usando stream de microfone existente");
        } else {
          this.stream = await navigator.mediaDevices.getUserMedia({
            audio: audioConstraints,
          });
          console.log("Criando novo stream de microfone");
        }

        // Verifica se o stream está ativo e funcionando
        const audioTracks = this.stream.getAudioTracks();
        console.log(`[AudioRecorder] Stream ativo: ${this.stream.active}`);
        console.log(
          `[AudioRecorder] Número de tracks de áudio: ${audioTracks.length}`
        );

        if (audioTracks.length > 0) {
          const track = audioTracks[0];
          console.log(
            `[AudioRecorder] Track ativa: ${track.enabled}, Estado: ${track.readyState}`
          );
          console.log(
            `[AudioRecorder] Configurações da track:`,
            track.getSettings()
          );
        }

        // Optional: Log the actual sample rate obtained for diagnostics
        // const audioTrack = this.stream.getAudioTracks()[0];
        // if (audioTrack) {
        //   const settings = audioTrack.getSettings();
        //   console.log(`[AudioRecorder] Requested mic SR: ${this.sampleRate}, Actual SR: ${settings.sampleRate}`);
        // }

        // Create AudioContext with iOS/Safari-friendly settings
        const contextOptions: AudioContextOptions = {};
        if (!isIOS && !isSafari) {
          contextOptions.sampleRate = this.sampleRate;
        }

        console.log("[AudioRecorder] Tentando criar AudioContext...");
        this.audioContext = await audioContext(contextOptions);
        console.log(
          `[AudioRecorder] AudioContext criado. Estado: ${this.audioContext.state}, Sample Rate: ${this.audioContext.sampleRate}`
        );

        // iOS/Safari requires user interaction to start AudioContext
        if (this.audioContext.state === "suspended") {
          try {
            await this.audioContext.resume();
            console.log("AudioContext resumed successfully");
          } catch (resumeError) {
            console.error(
              "Error resuming AudioContext in AudioRecorder:",
              resumeError
            );
            // For iOS/Safari, this might require a user gesture
            if (isIOS || isSafari) {
              console.warn(
                "iOS/Safari detected: AudioContext may need user interaction to resume"
              );
              // Tenta novamente após um pequeno delay
              setTimeout(async () => {
                try {
                  if (
                    this.audioContext &&
                    this.audioContext.state === "suspended"
                  ) {
                    await this.audioContext.resume();
                    console.log("AudioContext resumed on retry");
                  }
                } catch (retryError) {
                  console.error(
                    "Failed to resume AudioContext on retry:",
                    retryError
                  );
                }
              }, 100);
            }
          }
        }

        console.log("[AudioRecorder] Criando MediaStreamSource...");
        this.source = this.audioContext.createMediaStreamSource(this.stream);
        console.log("[AudioRecorder] MediaStreamSource criado com sucesso");

        console.log("[AudioRecorder] Configurando worklet de gravação...");
        const recorderWorkletName = "audio-recorder-worklet";
        const recorderWorkletSrc = createWorketFromSrc(
          recorderWorkletName,
          AudioRecordingWorklet
        );
        try {
          await this.audioContext.audioWorklet.addModule(recorderWorkletSrc);
          console.log(
            "[AudioRecorder] Worklet de gravação adicionado com sucesso"
          );
        } catch (moduleError) {
          console.error(
            `Failed to add AudioWorklet module: ${recorderWorkletName}`,
            moduleError
          );
          reject(moduleError);
          return;
        }
        console.log("[AudioRecorder] Criando AudioWorkletNode de gravação...");
        this.recordingWorklet = new AudioWorkletNode(
          this.audioContext,
          recorderWorkletName
        );

        console.log(
          "[AudioRecorder] Configurando handler de mensagens do worklet..."
        );
        this.recordingWorklet.port.onmessage = (ev: MessageEvent) => {
          if (ev.data && ev.data.data && ev.data.data.int16arrayBuffer) {
            const arrayBuffer = ev.data.data.int16arrayBuffer;
            const arrayBufferString = arrayBufferToBase64(arrayBuffer);
            // Log apenas ocasionalmente para não poluir o console
            if (Math.random() < 0.01) {
              // 1% das vezes
              console.log(
                `[AudioRecorder] Dados de áudio capturados: ${arrayBuffer.byteLength} bytes`
              );
            }
            this.emit("data", arrayBufferString);
          }
        };

        console.log(
          "[AudioRecorder] Conectando source ao worklet de gravação..."
        );
        this.source.connect(this.recordingWorklet);

        const vuWorkletName = "vu-meter";
        const vuMeterWorkletSrc = createWorketFromSrc(
          vuWorkletName,
          VolMeterWorket
        );
        try {
          await this.audioContext.audioWorklet.addModule(vuMeterWorkletSrc);
        } catch (moduleError) {
          console.error(
            `Failed to add AudioWorklet module: ${vuWorkletName}`,
            moduleError
          );
          // Continue without VU meter if it fails, or reject if critical
        }
        if (this.audioContext) {
          // Check if audioContext is still valid
          this.vuWorklet = new AudioWorkletNode(
            this.audioContext,
            vuWorkletName
          );
          this.vuWorklet.port.onmessage = (ev: MessageEvent) => {
            if (ev.data && typeof ev.data.volume === "number") {
              if (ev.data.volume > 0.01) {
                // Log apenas quando há volume significativo
                console.log(
                  `[AudioRecorder] Volume detectado: ${ev.data.volume.toFixed(
                    4
                  )}`
                );
              }
              this.emit("volume", ev.data.volume);
            }
          };
          this.source.connect(this.vuWorklet);
        }

        this.recording = true;
        console.log(
          "[AudioRecorder] ✅ AudioRecorder iniciado com sucesso! Pronto para capturar áudio."
        );
        resolve();
      } catch (err) {
        console.error("Error starting audio recorder:", err);

        // Fornece mensagens de erro mais específicas
        let errorMessage = "Erro desconhecido ao inicializar o microfone";
        if (err instanceof Error) {
          if (err.name === "NotAllowedError") {
            errorMessage =
              "Permissão do microfone foi negada. Por favor, permita o acesso ao microfone e recarregue a página.";
          } else if (err.name === "NotFoundError") {
            errorMessage =
              "Nenhum microfone foi encontrado. Verifique se há um microfone conectado.";
          } else if (err.name === "NotReadableError") {
            errorMessage =
              "Microfone está sendo usado por outro aplicativo. Feche outros aplicativos que possam estar usando o microfone.";
          } else if (err.name === "OverconstrainedError") {
            errorMessage =
              "Configurações do microfone não são suportadas. Tentando com configurações padrão...";
          } else {
            errorMessage = err.message;
          }
        }

        this.stop();
        reject(new Error(errorMessage));
      } finally {
        this.starting = null;
      }
    });
    return this.starting;
  }

  stop() {
    const handleStop = () => {
      this.recording = false;

      if (this.source) {
        this.source.disconnect();
        this.source = undefined;
      }

      if (this.recordingWorklet) {
        this.recordingWorklet.port.postMessage?.({ command: "stop" }); // Optional: command to worklet if needed
        this.recordingWorklet.port.close();
        this.recordingWorklet.disconnect();
        this.recordingWorklet = undefined;
      }

      if (this.vuWorklet) {
        this.vuWorklet.port.postMessage?.({ command: "stop" }); // Optional: command to worklet if needed
        this.vuWorklet.port.close();
        this.vuWorklet.disconnect();
        this.vuWorklet = undefined;
      }

      if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = undefined;
      }

      // Do not close the audioContext here, as it might be shared or managed by the `audioContext` utility.
      // If this AudioRecorder created its own exclusive context AND it's certain it won't be reused,
      // then `this.audioContext.close()` could be considered.
      // this.audioContext = undefined; // Keep if context is managed externally or reused.

      this.emit("stop");
    };

    if (this.starting) {
      this.starting
        .catch(() => {
          /* Ignore errors from an aborted start attempt */
        })
        .finally(() => {
          // Ensure starting is null before calling handleStop to prevent re-entrancy issues
          // if handleStop itself calls stop() or interacts with this.starting
          if (this.starting) this.starting = null;
          handleStop();
        });
    } else {
      handleStop();
    }
  }
}
