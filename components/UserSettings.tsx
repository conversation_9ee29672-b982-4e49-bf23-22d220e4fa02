/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import Modal from './Modal';
import { useUI, useUser } from '@/lib/state';

export default function UserSettings() {
  const { name, info, setName, setInfo } = useUser();
  const { setShowUserConfig } = useUI();

  function updateClient() {
    setShowUserConfig(false);
  }

  return (
    <Modal onClose={() => setShowUserConfig(false)}>
      <div className="userSettings">
       
        <form
          onSubmit={e => {
            e.preventDefault();
            setShowUserConfig(false);
            updateClient();
          }}
        >
          <div>
            Seu nome
            <input
              type="text"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Como quer ser chamado?"
            />
          </div>

          <div>
            Sobre você
            <textarea
              rows={3}
              value={info}
              onChange={e => setInfo(e.target.value)}
              placeholder="Coisas que o agente deveria saber sobre você, como curisiodades, gostos. Isso ajuda a conversa ganhar profundidade."
            />
          </div>

          <button className="button primary">Conversar</button>
        </form>
      </div>
    </Modal>
  );
}
