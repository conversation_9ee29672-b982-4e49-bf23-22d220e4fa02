/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import cn from "classnames";

import { memo, ReactNode, useEffect, useRef, useState } from "react";
import { AudioRecorder } from "../../../lib/audio-recorder";

import { useLiveAPIContext } from "../../../contexts/LiveAPIContext";
import { useUI } from "@/lib/state";

export type ControlTrayProps = {
  children?: ReactNode;
};

function ControlTray({ children }: ControlTrayProps) {
  const [audioRecorder] = useState(() => new AudioRecorder());
  const [muted, setMuted] = useState(false);
  const connectButtonRef = useRef<HTMLButtonElement>(null);
  const [iosAudioStatus, setIosAudioStatus] = useState<string>("");
  const [microphonePermission, setMicrophonePermission] = useState<
    "unknown" | "granted" | "denied" | "requesting"
  >("unknown");
  const microphoneStreamRef = useRef<MediaStream | null>(null);

  const { showAgentEdit, showUserConfig } = useUI();
  const { client, connected, connect, disconnect } = useLiveAPIContext();

  const checkIOSAudioStatus = () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(() => setIosAudioStatus("Permissão de microfone concedida"))
        .catch((err) => setIosAudioStatus(`Erro: ${err.message}`));
    }
  };

  // Função para solicitar permissão do microfone antecipadamente
  const requestMicrophonePermission = async (): Promise<boolean> => {
    setMicrophonePermission("requesting");

    try {
      // Detecta iOS e Safari para configurações específicas
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isSafari = /^((?!chrome|android).)*safari/i.test(
        navigator.userAgent
      );

      // Configurações de áudio otimizadas para iOS/Safari
      const audioConstraints: MediaTrackConstraints = {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      };

      // Só define sampleRate se não for iOS/Safari
      if (!isIOS && !isSafari) {
        audioConstraints.sampleRate = 16000;
      }

      // Solicita permissão do microfone
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: audioConstraints,
      });

      // Para Safari, mantemos o stream ativo por um tempo para evitar nova solicitação
      if (isSafari) {
        microphoneStreamRef.current = stream;
        setIosAudioStatus("Stream do microfone ativo para Safari");

        // Para o stream após 30 segundos se não for usado
        setTimeout(() => {
          if (microphoneStreamRef.current && !connected) {
            microphoneStreamRef.current
              .getTracks()
              .forEach((track) => track.stop());
            microphoneStreamRef.current = null;
            console.log("Stream do microfone parado automaticamente");
          }
        }, 30000);
      } else {
        // Para outros navegadores, para o stream imediatamente
        stream.getTracks().forEach((track) => track.stop());
      }

      setMicrophonePermission("granted");
      setIosAudioStatus("Permissão de microfone concedida");

      console.log("Permissão do microfone obtida com sucesso");
      return true;
    } catch (error) {
      console.error("Erro ao solicitar permissão do microfone:", error);
      setMicrophonePermission("denied");
      setIosAudioStatus(
        `Erro: ${error instanceof Error ? error.message : "Permissão negada"}`
      );
      return false;
    }
  };

  // Verifica permissões quando o componente monta
  useEffect(() => {
    const checkInitialPermissions = async () => {
      const isSafari = /^((?!chrome|android).)*safari/i.test(
        navigator.userAgent
      );

      // Para Safari, não confiamos na Permissions API para microfone
      // porque ela pode mostrar "granted" mesmo quando o acesso real não foi concedido
      if (isSafari) {
        setMicrophonePermission("unknown");
        setIosAudioStatus(
          "Safari detectado - permissão será verificada ao conectar"
        );
        return;
      }

      try {
        const permissions = await navigator.permissions.query({
          name: "microphone" as PermissionName,
        });
        if (permissions.state === "granted") {
          setMicrophonePermission("granted");
          setIosAudioStatus("Permissão já concedida");
        } else if (permissions.state === "denied") {
          setMicrophonePermission("denied");
          setIosAudioStatus("Permissão negada");
        }

        // Escuta mudanças nas permissões
        permissions.onchange = () => {
          if (permissions.state === "granted") {
            setMicrophonePermission("granted");
            setIosAudioStatus("Permissão concedida");
          } else if (permissions.state === "denied") {
            setMicrophonePermission("denied");
            setIosAudioStatus("Permissão negada");
          }
        };
      } catch (error) {
        // Alguns navegadores não suportam permissions.query para microfone
        console.log("Permissions API não disponível para microfone");
        setMicrophonePermission("unknown");
      }
    };

    checkInitialPermissions();
  }, []);

  // Cleanup do stream quando o componente desmonta
  useEffect(() => {
    return () => {
      if (microphoneStreamRef.current) {
        microphoneStreamRef.current
          .getTracks()
          .forEach((track) => track.stop());
        microphoneStreamRef.current = null;
        console.log("Stream do microfone limpo no cleanup");
      }
    };
  }, []);

  // Stop the current agent if the user is editing the agent or user config
  useEffect(() => {
    if (showAgentEdit || showUserConfig) {
      if (connected) disconnect();
    }
  }, [showUserConfig, showAgentEdit, connected, disconnect]);

  useEffect(() => {
    if (!connected && connectButtonRef.current) {
      connectButtonRef.current.focus();
    }
  }, [connected]);

  useEffect(() => {
    const onData = (base64: string) => {
      console.log(`[ControlTray] Enviando dados de áudio: ${base64.length} caracteres`);
      client.sendRealtimeInput([
        {
          mimeType: "audio/pcm;rate=16000",
          data: base64,
        },
      ]);
    };
    if (connected && !muted && audioRecorder) {
      // Para Safari, passa o stream existente se disponível
      const isSafari = /^((?!chrome|android).)*safari/i.test(
        navigator.userAgent
      );
      const streamToUse = isSafari ? microphoneStreamRef.current : undefined;

      audioRecorder
        .on("data", onData)
        .start(streamToUse || undefined)
        .catch((error: any) => {
          console.error("Failed to start audio recorder:", error);

          // Mostra mensagem de erro específica
          const errorMessage =
            error instanceof Error
              ? error.message
              : "Erro ao inicializar o microfone";

          // Detecta iOS/Safari para mensagens específicas
          const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
          const isSafari = /^((?!chrome|android).)*safari/i.test(
            navigator.userAgent
          );

          if (isIOS || isSafari) {
            alert(
              `${errorMessage}\n\nPara usar o microfone no Safari/iOS:\n1. Certifique-se de que o site está sendo acessado via HTTPS\n2. Permita o acesso ao microfone quando solicitado\n3. Se necessário, verifique as configurações de privacidade do navegador`
            );
          } else {
            alert(errorMessage);
          }

          // Reset do estado de permissão em caso de erro
          setMicrophonePermission("denied");
        });
    } else {
      audioRecorder.stop();

      // Limpa o stream do Safari quando desconecta
      const isSafari = /^((?!chrome|android).)*safari/i.test(
        navigator.userAgent
      );
      if (isSafari && microphoneStreamRef.current) {
        microphoneStreamRef.current
          .getTracks()
          .forEach((track) => track.stop());
        microphoneStreamRef.current = null;
        setIosAudioStatus("Stream do microfone parado");
        console.log("Stream do microfone parado ao desconectar");
      }
    }
    return () => {
      audioRecorder.off("data", onData);
    };
  }, [connected, client, muted, audioRecorder]);

  const initializeAudioForIOS = async () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    if (isIOS || isSafari) {
      try {
        // Crie um AudioContext temporário e reproduza um som silencioso
        const tempContext = new AudioContext();
        const oscillator = tempContext.createOscillator();
        oscillator.connect(tempContext.destination);
        oscillator.start(0);
        oscillator.stop(0.001);

        // Aguarde um pouco para garantir que o iOS/Safari processou a interação
        await new Promise((resolve) => setTimeout(resolve, 100));

        console.log("Audio initialized for iOS/Safari");
      } catch (e) {
        console.error("Failed to initialize audio for iOS/Safari:", e);
      }
    }
  };

  const handleConnect = async () => {
    try {
      // 1. Primeiro, solicita permissão do microfone
      const hasPermission = await requestMicrophonePermission();

      if (!hasPermission) {
        alert(
          "É necessário permitir o acesso ao microfone para usar o assistente de voz."
        );
        return;
      }

      // 2. Inicializa áudio para iOS/Safari
      await initializeAudioForIOS();

      // 3. Conecta ao serviço
      await connect();
    } catch (error) {
      console.error("Erro ao conectar:", error);
      setMicrophonePermission("denied");
      alert("Erro ao conectar. Verifique suas permissões e tente novamente.");
    }
  };

  return (
    <section className="control-tray">
      <nav className={cn("actions-nav", { disabled: !connected })}>
        <button
          className={cn("action-button mic-button")}
          onClick={() => setMuted(!muted)}
        >
          {!muted ? (
            <span className="material-symbols-outlined filled">mic</span>
          ) : (
            <span className="material-symbols-outlined filled">mic_off</span>
          )}
        </button>
        {children}
      </nav>

      <div className={cn("connection-container", { connected })}>
        <div className="connection-button-container">
          <button
            ref={connectButtonRef}
            className={cn("action-button connect-toggle", { connected })}
            onClick={async () => {
              if (connected) {
                disconnect();
              } else {
                await handleConnect();
              }
            }}
            disabled={microphonePermission === "requesting"}
          >
            <span className="material-symbols-outlined filled">
              {microphonePermission === "requesting"
                ? "hourglass_empty"
                : connected
                ? "pause"
                : "play_arrow"}
            </span>
          </button>
        </div>
        <span className="text-indicator">Streaming</span>
      </div>
      {(/iPad|iPhone|iPod/.test(navigator.userAgent) ||
        /^((?!chrome|android).)*safari/i.test(navigator.userAgent)) && (
        <div className="ios-debug">
          <p>
            Status do Microfone:{" "}
            {microphonePermission === "unknown"
              ? "Não verificado"
              : microphonePermission === "requesting"
              ? "Solicitando permissão..."
              : microphonePermission === "granted"
              ? "Permitido ✓"
              : "Negado ✗"}
          </p>
          {iosAudioStatus && <p>Detalhes: {iosAudioStatus}</p>}
          <button onClick={checkIOSAudioStatus}>Verificar Microfone</button>
        </div>
      )}
    </section>
  );
}

export default memo(ControlTray);
