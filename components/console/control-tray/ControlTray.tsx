/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import cn from "classnames";

import { memo, ReactNode, useEffect, useRef, useState } from "react";
import { AudioRecorder } from "../../../lib/audio-recorder";

import { useLiveAPIContext } from "../../../contexts/LiveAPIContext";
import { useUI } from "@/lib/state";

export type ControlTrayProps = {
  children?: ReactNode;
};

function ControlTray({ children }: ControlTrayProps) {
  const [audioRecorder] = useState(() => new AudioRecorder());
  const [muted, setMuted] = useState(false);
  const connectButtonRef = useRef<HTMLButtonElement>(null);
  const [iosAudioStatus, setIosAudioStatus] = useState<string>("");

  const { showAgentEdit, showUserConfig } = useUI();
  const { client, connected, connect, disconnect } = useLiveAPIContext();

  const checkIOSAudioStatus = () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(() => setIosAudioStatus("Permissão de microfone concedida"))
        .catch((err) => setIosAudioStatus(`Erro: ${err.message}`));
    }
  };

  // Stop the current agent if the user is editing the agent or user config
  useEffect(() => {
    if (showAgentEdit || showUserConfig) {
      if (connected) disconnect();
    }
  }, [showUserConfig, showAgentEdit, connected, disconnect]);

  useEffect(() => {
    if (!connected && connectButtonRef.current) {
      connectButtonRef.current.focus();
    }
  }, [connected]);

  useEffect(() => {
    const onData = (base64: string) => {
      client.sendRealtimeInput([
        {
          mimeType: "audio/pcm;rate=16000",
          data: base64,
        },
      ]);
    };
    if (connected && !muted && audioRecorder) {
      audioRecorder
        .on("data", onData)
        .start()
        .catch((error: any) => {
          console.error("Failed to start audio recorder:", error);
          // Show user-friendly error message for iOS
          const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
          if (isIOS) {
            alert(
              "Para usar o microfone no iPhone, certifique-se de que:\n1. O site está sendo acessado via HTTPS\n2. Você deu permissão para usar o microfone\n3. Toque no botão de conectar novamente"
            );
          }
        });
    } else {
      audioRecorder.stop();
    }
    return () => {
      audioRecorder.off("data", onData);
    };
  }, [connected, client, muted, audioRecorder]);

  const initializeAudioForIOS = async () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      try {
        // Crie um AudioContext temporário e reproduza um som silencioso
        const tempContext = new AudioContext();
        const oscillator = tempContext.createOscillator();
        oscillator.connect(tempContext.destination);
        oscillator.start(0);
        oscillator.stop(0.001);

        // Aguarde um pouco para garantir que o iOS processou a interação
        await new Promise((resolve) => setTimeout(resolve, 100));

        console.log("Audio initialized for iOS");
      } catch (e) {
        console.error("Failed to initialize audio for iOS:", e);
      }
    }
  };

  return (
    <section className="control-tray">
      <nav className={cn("actions-nav", { disabled: !connected })}>
        <button
          className={cn("action-button mic-button")}
          onClick={() => setMuted(!muted)}
        >
          {!muted ? (
            <span className="material-symbols-outlined filled">mic</span>
          ) : (
            <span className="material-symbols-outlined filled">mic_off</span>
          )}
        </button>
        {children}
      </nav>

      <div className={cn("connection-container", { connected })}>
        <div className="connection-button-container">
          <button
            ref={connectButtonRef}
            className={cn("action-button connect-toggle", { connected })}
            onClick={async () => {
              await initializeAudioForIOS();
              connected ? disconnect() : connect();
            }}
          >
            <span className="material-symbols-outlined filled">
              {connected ? "pause" : "play_arrow"}
            </span>
          </button>
        </div>
        <span className="text-indicator">Streaming</span>
      </div>
      {/iPad|iPhone|iPod/.test(navigator.userAgent) && (
        <div className="ios-debug">
          <p>Status iOS: {iosAudioStatus}</p>
          <button onClick={checkIOSAudioStatus}>Verificar Microfone</button>
        </div>
      )}
    </section>
  );
}

export default memo(ControlTray);
