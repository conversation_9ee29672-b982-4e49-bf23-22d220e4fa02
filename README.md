# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

## Run Locally

**Prerequisites:** Node.js

1. Install dependencies:
   `npm install`
2. Set the `GEMINI_API_KEY` in [.env.local](.env.local) to your Gemini API key
3. Run the app:
   `npm run dev`

## iOS Support

This app has been optimized for iOS devices, including iPhone and iPad:

- **HTTPS Support**: The development server automatically uses HTTPS to enable microphone access on iOS
- **PWA Manifest**: Includes a web app manifest for better iOS integration
- **iOS-specific Audio Settings**: Audio constraints are optimized for iOS compatibility
- **User Interaction Requirements**: AudioContext is properly initialized after user interaction

### Using on iPhone/iPad:

1. Access the app via HTTPS (automatically enabled in development)
2. Grant microphone permissions when prompted
3. Tap the connect button to start the audio session
4. If microphone doesn't work, try refreshing and tapping connect again

### Troubleshooting iOS Issues:

- Ensure you're accessing via HTTPS
- Check that microphone permissions are granted in Safari settings
- Try refreshing the page and reconnecting
- Make sure you tap the connect button (user interaction is required for AudioContext)
