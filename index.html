<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, user-scalable=no"
    />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Google I/O 2025 Live API Demo" />

    <!-- iOS specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="POC Audio Agent" />
    <meta name="apple-touch-fullscreen" content="yes" />

    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200&display=block"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="index.css" />
    <title>POC Taya Audio Agent</title>
    <script type="importmap">
      {
        "imports": {
          "react/": "https://esm.sh/react@^19.1.0/",
          "react": "https://esm.sh/react@^19.1.0",
          "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
          "path": "https://esm.sh/path@^0.12.7",
          "vite": "https://esm.sh/vite@^6.3.5",
          "classnames": "https://esm.sh/classnames@^2.5.1",
          "@google/genai": "https://esm.sh/@google/genai@^1.0.0",
          "zustand": "https://esm.sh/zustand@^5.0.4",
          "eventemitter3": "https://esm.sh/eventemitter3@^5.0.1",
          "lodash": "https://esm.sh/lodash@^4.17.21"
        }
      }
    </script>
    <link rel="stylesheet" href="/index.css" />
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script src="index.tsx" type="module"></script>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
<link rel="stylesheet" href="index.css" />
<script src="index.tsx" type="module"></script>
